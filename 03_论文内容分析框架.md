# 论文内容AIGC风险分析框架

## 一、分析目标
对现有论文内容进行系统性分析，识别可能被AIGC检测系统标记的段落和写作特征，为后续改写提供精准指导。

## 二、分析维度

### 2.1 语言标记分析
**检查要点：**
- 开头句式是否使用AI常用模板
- 过渡词汇是否过于机械化
- 结论表述是否过于标准化
- 学术表达是否缺乏个人色彩

**风险等级评估：**
- 高风险：大量使用AI标记词汇
- 中风险：部分使用标准化表达
- 低风险：语言表达相对自然

### 2.2 句式结构分析
**检查要点：**
- 句子长度是否过于均匀
- 句式结构是否过于规整
- 复合句使用是否过于规律
- 语序是否过于标准化

**评估标准：**
- 句长变化系数
- 句式类型多样性
- 语法结构复杂度
- 表达方式灵活性

### 2.3 词汇使用分析
**检查要点：**
- 专业术语使用频率
- 同义词替换模式
- 词汇丰富度水平
- 个性化表达程度

**分析指标：**
- 词汇重复率
- 术语标准化程度
- 表达创新性
- 个人风格体现

### 2.4 论证结构分析
**检查要点：**
- 论证逻辑是否过于线性
- 段落结构是否过于标准
- 论据选择是否过于典型
- 结论表述是否过于绝对

**评估维度：**
- 逻辑发展自然性
- 论证层次丰富性
- 材料选择独特性
- 观点表达个性化

## 三、具体分析方法

### 3.1 段落级别分析
**分析步骤：**
1. 逐段检查开头句式
2. 识别段内过渡词汇
3. 分析段落结构模式
4. 评估内容原创程度

**标记系统：**
- 🔴 高风险段落：需要重点改写
- 🟡 中风险段落：需要适度调整
- 🟢 低风险段落：可以保留或微调

### 3.2 句子级别分析
**分析要点：**
1. 句式模板化程度
2. 词汇选择标准化
3. 语法结构规律性
4. 表达方式个性化

**改写优先级：**
- 优先级1：AI标记词汇密集句
- 优先级2：结构过于标准句
- 优先级3：表达过于客观句

### 3.3 词汇级别分析
**重点关注：**
1. AI高频触发词汇
2. 过度标准化表达
3. 缺乏个人特色词汇
4. 学科专业术语使用

**处理策略：**
- 替换：AI标记明显词汇
- 调整：过于标准表达
- 增加：个性化词汇
- 优化：专业术语使用

## 四、中文文学论文特殊分析

### 4.1 学科特色检查
**中文文学特点：**
- 文本解读的深度和独特性
- 文化背景的融入程度
- 审美判断的个人色彩
- 传统文化元素的运用

**AI痕迹识别：**
- 文学分析过于表面化
- 缺乏个人审美体验
- 文化阐释过于标准
- 术语使用过于机械

### 4.2 写作风格分析
**人文学科特色：**
- 思辨性强于实证性
- 主观判断与客观分析结合
- 文学感悟与理论分析并重
- 个人见解与学术规范平衡

**风险点识别：**
- 过于客观的文学评价
- 缺乏个人情感投入
- 分析角度过于常见
- 表达方式过于标准

## 五、分析报告模板

### 5.1 整体风险评估
```
论文标题：[待填入]
总字数：[待填入]
整体风险等级：[高/中/低]
预估AIGC检测率：[百分比]
主要风险点：[列举]
改写工作量：[大/中/小]
```

### 5.2 分段风险分析
```
段落编号：[X]
段落主题：[概述]
风险等级：[🔴/🟡/🟢]
主要问题：[具体描述]
改写建议：[具体指导]
预期效果：[改写后预期]
```

### 5.3 重点问题汇总
```
高频AI标记词汇：[列举]
标准化句式模板：[列举]
过于规整的结构：[描述]
缺乏个性的表达：[举例]
改写优先级排序：[列表]
```

## 六、改写指导原则

### 6.1 保持学术质量
- 确保论证逻辑清晰
- 维持学术表达规范
- 保证内容深度适当
- 体现专业学术素养

### 6.2 增强人工特色
- 融入个人观点判断
- 体现独特分析角度
- 使用个性化表达
- 展现学科专业背景

### 6.3 优化语言表达
- 增加句式变化
- 丰富词汇选择
- 自然化过渡连接
- 个性化结论表述

## 七、质量控制标准

### 7.1 改写前检查
- 识别所有高风险段落
- 标记重点问题句子
- 列出改写优先级
- 制定具体改写计划

### 7.2 改写后验证
- 语言自然度提升
- AI标记词汇减少
- 个人特色增强
- 学术质量保持

### 7.3 最终质量评估
- AIGC检测率达标
- 语言表达流畅
- 学术内容完整
- 个人风格突出

## 八、使用说明

### 8.1 分析流程
1. 提供完整论文内容
2. 按照框架逐项分析
3. 生成详细分析报告
4. 制定改写实施计划
5. 执行改写并验证效果

### 8.2 注意事项
- 分析要全面细致
- 标记要准确明确
- 建议要具体可行
- 改写要保质保量

### 8.3 预期成果
- 准确识别风险点
- 制定有效改写策略
- 显著降低AIGC检测率
- 保持学术写作质量

---

**请提供您的论文内容，我将根据此框架进行详细分析，为您制定精准的降重改写方案。**
